package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"web-analyzer/models"
)

func TestAnalyzeHandler_MethodNotAllowed(t *testing.T) {
	req, err := http.NewRequest("GET", "/analyze", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(AnalyzeHandler)
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusMethodNotAllowed {
		t.<PERSON>rf("handler returned wrong status code: got %v want %v",
			status, http.StatusMethodNotAllowed)
	}
}

func TestAnalyzeHandler_InvalidJSON(t *testing.T) {
	req, err := http.NewRequest("POST", "/analyze", bytes.NewBuffer([]byte("invalid json")))
	if err != nil {
		t.<PERSON>al(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(AnalyzeHandler)
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusBadRequest)
	}
}

func TestAnalyzeHandler_EmptyURL(t *testing.T) {
	reqBody := models.AnalyzeRequest{URL: ""}
	jsonBody, _ := json.Marshal(reqBody)

	req, err := http.NewRequest("POST", "/analyze", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(AnalyzeHandler)
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusBadRequest)
	}
}

func TestAnalyzeHandler_InvalidURL(t *testing.T) {
	reqBody := models.AnalyzeRequest{URL: "not-a-valid-url"}
	jsonBody, _ := json.Marshal(reqBody)

	req, err := http.NewRequest("POST", "/analyze", bytes.NewBuffer(jsonBody))
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	handler := http.HandlerFunc(AnalyzeHandler)
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusBadRequest)
	}
}

func TestValidateURL(t *testing.T) {
	tests := []struct {
		name    string
		url     string
		wantErr bool
	}{
		{"Valid HTTP URL", "http://example.com", false},
		{"Valid HTTPS URL", "https://example.com", false},
		{"URL with path", "https://example.com/path", false},
		{"URL with query", "https://example.com?query=value", false},
		{"No scheme", "example.com", true},
		{"Invalid scheme", "ftp://example.com", true},
		{"No host", "https://", true},
		{"Empty URL", "", true},
		{"Invalid format", "not-a-url", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateURL(tt.url)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateURL() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
