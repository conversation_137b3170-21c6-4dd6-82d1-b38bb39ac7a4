package utils

import (
	"fmt"
	"net/http"
	"web-analyzer/models"
)

// HTTPError represents an HTTP-related error with status code
type HTTPError struct {
	StatusCode int
	Message    string
}

func (e *HTTPError) Error() string {
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
}

// AnalyzeWebPage performs comprehensive analysis of a web page
func AnalyzeWebPage(url string) (*models.AnalyzeResponse, error) {
	// Fetch the web page
	resp, err := http.Get(url)
	if err != nil {
		return nil, &HTTPError{
			StatusCode: http.StatusBadGateway,
			Message:    fmt.Sprintf("Failed to fetch URL: %s", err.Error()),
		}
	}
	defer resp.Body.Close()

	// Check if the response is successful
	if resp.StatusCode != http.StatusOK {
		return nil, &HTTPError{
			StatusCode: resp.StatusCode,
			Message:    fmt.Sprintf("URL returned status %d: %s", resp.StatusCode, resp.Status),
		}
	}

	// Parse HTML and perform analysis
	htmlVersion, err := DetectHTMLVersion(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to detect HTML version: %w", err)
	}

	// Reset body for subsequent parsing
	resp.Body.Close()
	resp, err = http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	title, err := ExtractTitle(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to extract title: %w", err)
	}

	// Reset body for subsequent parsing
	resp.Body.Close()
	resp, err = http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	headings, err := AnalyzeHeadings(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze headings: %w", err)
	}

	// Reset body for subsequent parsing
	resp.Body.Close()
	resp, err = http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	links, err := AnalyzeLinks(resp.Body, url)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze links: %w", err)
	}

	// Reset body for subsequent parsing
	resp.Body.Close()
	resp, err = http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	hasLoginForm, err := DetectLoginForm(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to detect login form: %w", err)
	}

	return &models.AnalyzeResponse{
		HTMLVersion:  htmlVersion,
		Title:        title,
		Headings:     headings,
		Links:        *links,
		HasLoginForm: hasLoginForm,
	}, nil
}
