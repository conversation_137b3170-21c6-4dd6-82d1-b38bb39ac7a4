package utils

import (
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
	"web-analyzer/models"

	"golang.org/x/net/html"
)

// AnalyzeLinks extracts and analyzes all links in the HTML document
func AnalyzeLinks(body io.Reader, baseURL string) (*models.LinkAnalysis, error) {
	doc, err := html.Parse(body)
	if err != nil {
		return nil, err
	}

	// Parse base URL for comparison
	base, err := url.Parse(baseURL)
	if err != nil {
		return nil, err
	}

	// Extract all links
	links := extractLinks(doc, base)

	// Categorize links
	internal := 0
	external := 0
	inaccessible := 0

	// Use channels and goroutines to check link accessibility concurrently
	type linkResult struct {
		isInternal   bool
		isAccessible bool
	}

	results := make(chan linkResult, len(links))
	var wg sync.WaitGroup

	// Check each link
	for _, link := range links {
		wg.Add(1)
		go func(linkURL string, baseHost string) {
			defer wg.Done()
			
			parsedLink, err := url.Parse(linkURL)
			if err != nil {
				results <- linkResult{isInternal: false, isAccessible: false}
				return
			}

			// Determine if link is internal or external
			isInternal := isInternalLink(parsedLink, baseHost)
			
			// Check accessibility (with timeout)
			isAccessible := checkLinkAccessibility(linkURL)
			
			results <- linkResult{isInternal: isInternal, isAccessible: isAccessible}
		}(link, base.Host)
	}

	// Close results channel when all goroutines complete
	go func() {
		wg.Wait()
		close(results)
	}()

	// Collect results
	for result := range results {
		if result.isInternal {
			internal++
		} else {
			external++
		}
		
		if !result.isAccessible {
			inaccessible++
		}
	}

	return &models.LinkAnalysis{
		Internal:     internal,
		External:     external,
		Inaccessible: inaccessible,
	}, nil
}

// extractLinks finds all href attributes in anchor tags
func extractLinks(n *html.Node, base *url.URL) []string {
	var links []string

	if n.Type == html.ElementNode && n.Data == "a" {
		for _, attr := range n.Attr {
			if attr.Key == "href" && attr.Val != "" {
				// Resolve relative URLs
				if resolvedURL := resolveURL(attr.Val, base); resolvedURL != "" {
					links = append(links, resolvedURL)
				}
			}
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		links = append(links, extractLinks(c, base)...)
	}

	return links
}

// resolveURL converts relative URLs to absolute URLs
func resolveURL(href string, base *url.URL) string {
	// Skip empty, javascript:, mailto:, tel:, and fragment-only links
	if href == "" || strings.HasPrefix(href, "#") || 
		strings.HasPrefix(href, "javascript:") || 
		strings.HasPrefix(href, "mailto:") || 
		strings.HasPrefix(href, "tel:") {
		return ""
	}

	parsed, err := url.Parse(href)
	if err != nil {
		return ""
	}

	resolved := base.ResolveReference(parsed)
	return resolved.String()
}

// isInternalLink determines if a link is internal to the same domain
func isInternalLink(linkURL *url.URL, baseHost string) bool {
	// If no host specified, it's relative (internal)
	if linkURL.Host == "" {
		return true
	}
	
	// Compare hosts (case-insensitive)
	return strings.EqualFold(linkURL.Host, baseHost)
}

// checkLinkAccessibility tests if a link is accessible
func checkLinkAccessibility(linkURL string) bool {
	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// Allow up to 5 redirects
			if len(via) >= 5 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// Make HEAD request to check accessibility
	resp, err := client.Head(linkURL)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// Consider 2xx and 3xx status codes as accessible
	return resp.StatusCode >= 200 && resp.StatusCode < 400
}
