package utils

import (
	"strings"
	"testing"
)

func TestDetectHTMLVersion(t *testing.T) {
	tests := []struct {
		name     string
		html     string
		expected string
	}{
		{
			name:     "HTML5 DOCTYPE",
			html:     `<!DOCTYPE html><html><head><title>Test</title></head><body></body></html>`,
			expected: "HTML5",
		},
		{
			name:     "HTML 4.01 Strict",
			html:     `<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"><html><head><title>Test</title></head><body></body></html>`,
			expected: "HTML 4.01 Strict",
		},
		{
			name:     "XHTML 1.0 Transitional",
			html:     `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html><head><title>Test</title></head><body></body></html>`,
			expected: "XHTML 1.0 Transitional",
		},
		{
			name:     "HTML5 with semantic elements",
			html:     `<html><head><title>Test</title></head><body><article>Content</article></body></html>`,
			expected: "HTML5 (inferred)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reader := strings.NewReader(tt.html)
			result, err := DetectHTMLVersion(reader)
			if err != nil {
				t.Errorf("DetectHTMLVersion() error = %v", err)
				return
			}
			if result != tt.expected {
				t.Errorf("DetectHTMLVersion() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestExtractTitle(t *testing.T) {
	tests := []struct {
		name     string
		html     string
		expected string
	}{
		{
			name:     "Simple title",
			html:     `<html><head><title>Test Page</title></head><body></body></html>`,
			expected: "Test Page",
		},
		{
			name:     "Title with whitespace",
			html:     `<html><head><title>  Test Page  </title></head><body></body></html>`,
			expected: "Test Page",
		},
		{
			name:     "No title",
			html:     `<html><head></head><body></body></html>`,
			expected: "",
		},
		{
			name:     "Empty title",
			html:     `<html><head><title></title></head><body></body></html>`,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reader := strings.NewReader(tt.html)
			result, err := ExtractTitle(reader)
			if err != nil {
				t.Errorf("ExtractTitle() error = %v", err)
				return
			}
			if result != tt.expected {
				t.Errorf("ExtractTitle() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestAnalyzeHeadings(t *testing.T) {
	html := `
	<html>
		<head><title>Test</title></head>
		<body>
			<h1>Heading 1</h1>
			<h1>Another H1</h1>
			<h2>Heading 2</h2>
			<h3>Heading 3</h3>
			<h3>Another H3</h3>
			<h3>Third H3</h3>
		</body>
	</html>`

	reader := strings.NewReader(html)
	result, err := AnalyzeHeadings(reader)
	if err != nil {
		t.Errorf("AnalyzeHeadings() error = %v", err)
		return
	}

	expected := map[string]int{
		"h1": 2,
		"h2": 1,
		"h3": 3,
		"h4": 0,
		"h5": 0,
		"h6": 0,
	}

	for level, count := range expected {
		if result[level] != count {
			t.Errorf("AnalyzeHeadings() %s = %v, want %v", level, result[level], count)
		}
	}
}

func TestDetectLoginForm(t *testing.T) {
	tests := []struct {
		name     string
		html     string
		expected bool
	}{
		{
			name: "Login form with password field",
			html: `<html><body>
				<form action="/login">
					<input type="text" name="username">
					<input type="password" name="password">
					<input type="submit" value="Login">
				</form>
			</body></html>`,
			expected: true,
		},
		{
			name: "Form without password field",
			html: `<html><body>
				<form action="/contact">
					<input type="text" name="name">
					<input type="email" name="email">
					<input type="submit" value="Submit">
				</form>
			</body></html>`,
			expected: false,
		},
		{
			name: "Login form with email field",
			html: `<html><body>
				<form id="loginForm">
					<input type="email" name="email">
					<input type="password" name="password">
					<input type="submit" value="Sign In">
				</form>
			</body></html>`,
			expected: true,
		},
		{
			name: "No forms",
			html: `<html><body>
				<h1>Welcome</h1>
				<p>This is a simple page with no forms.</p>
			</body></html>`,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reader := strings.NewReader(tt.html)
			result, err := DetectLoginForm(reader)
			if err != nil {
				t.Errorf("DetectLoginForm() error = %v", err)
				return
			}
			if result != tt.expected {
				t.Errorf("DetectLoginForm() = %v, want %v", result, tt.expected)
			}
		})
	}
}
