package utils

import (
	"io"
	"strings"

	"golang.org/x/net/html"
)

// DetectHTMLVersion analyzes the HTML document to determine its version
func DetectHTMLVersion(body io.Reader) (string, error) {
	doc, err := html.Parse(body)
	if err != nil {
		return "", err
	}

	// Look for DOCTYPE declaration
	doctype := findDoctype(doc)
	if doctype != "" {
		return parseDoctype(doctype), nil
	}

	// If no DOCTYPE found, try to infer from HTML structure
	return inferHTMLVersion(doc), nil
}

// ExtractTitle extracts the page title from HTML
func ExtractTitle(body io.Reader) (string, error) {
	doc, err := html.Parse(body)
	if err != nil {
		return "", err
	}

	title := findTitle(doc)
	return strings.TrimSpace(title), nil
}

// findDoctype searches for DOCTYPE declaration in the HTML document
func findDoctype(n *html.Node) string {
	if n.Type == html.DoctypeNode {
		// For DOCTYPE nodes, we need to reconstruct the full DOCTYPE string
		// from the node's attributes
		doctype := n.Data
		if len(n.Attr) > 0 {
			for _, attr := range n.Attr {
				if attr.Key == "public" {
					doctype += " PUBLIC \"" + attr.Val + "\""
				} else if attr.Key == "system" {
					doctype += " \"" + attr.Val + "\""
				}
			}
		}
		return doctype
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if doctype := findDoctype(c); doctype != "" {
			return doctype
		}
	}
	return ""
}

// parseDoctype determines HTML version from DOCTYPE string
func parseDoctype(doctype string) string {
	doctype = strings.ToLower(strings.TrimSpace(doctype))

	if doctype == "html" {
		return "HTML5"
	}

	if strings.Contains(doctype, "html 4.01") {
		if strings.Contains(doctype, "strict") {
			return "HTML 4.01 Strict"
		} else if strings.Contains(doctype, "transitional") {
			return "HTML 4.01 Transitional"
		} else if strings.Contains(doctype, "frameset") {
			return "HTML 4.01 Frameset"
		}
		return "HTML 4.01"
	}

	if strings.Contains(doctype, "xhtml 1.0") {
		if strings.Contains(doctype, "strict") {
			return "XHTML 1.0 Strict"
		} else if strings.Contains(doctype, "transitional") {
			return "XHTML 1.0 Transitional"
		} else if strings.Contains(doctype, "frameset") {
			return "XHTML 1.0 Frameset"
		}
		return "XHTML 1.0"
	}

	if strings.Contains(doctype, "xhtml 1.1") {
		return "XHTML 1.1"
	}

	if strings.Contains(doctype, "html 3.2") {
		return "HTML 3.2"
	}

	if strings.Contains(doctype, "html 2.0") {
		return "HTML 2.0"
	}

	return "Unknown HTML Version"
}

// inferHTMLVersion tries to infer HTML version from document structure
func inferHTMLVersion(n *html.Node) string {
	// Look for HTML5 specific elements
	if hasHTML5Elements(n) {
		return "HTML5 (inferred)"
	}

	// Default fallback
	return "HTML (version unknown)"
}

// hasHTML5Elements checks for HTML5-specific elements
func hasHTML5Elements(n *html.Node) bool {
	html5Elements := []string{"article", "aside", "details", "figcaption", "figure",
		"footer", "header", "main", "mark", "nav", "section", "summary", "time"}

	if n.Type == html.ElementNode {
		for _, elem := range html5Elements {
			if n.Data == elem {
				return true
			}
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if hasHTML5Elements(c) {
			return true
		}
	}

	return false
}

// findTitle searches for the title element in the HTML document
func findTitle(n *html.Node) string {
	if n.Type == html.ElementNode && n.Data == "title" {
		return getTextContent(n)
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if title := findTitle(c); title != "" {
			return title
		}
	}

	return ""
}

// getTextContent extracts text content from a node
func getTextContent(n *html.Node) string {
	if n.Type == html.TextNode {
		return n.Data
	}

	var text strings.Builder
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		text.WriteString(getTextContent(c))
	}

	return text.String()
}

// AnalyzeHeadings counts headings by level (h1-h6) in the HTML document
func AnalyzeHeadings(body io.Reader) (map[string]int, error) {
	doc, err := html.Parse(body)
	if err != nil {
		return nil, err
	}

	headings := map[string]int{
		"h1": 0,
		"h2": 0,
		"h3": 0,
		"h4": 0,
		"h5": 0,
		"h6": 0,
	}

	countHeadings(doc, headings)
	return headings, nil
}

// countHeadings recursively counts heading elements in the HTML tree
func countHeadings(n *html.Node, headings map[string]int) {
	if n.Type == html.ElementNode {
		switch n.Data {
		case "h1", "h2", "h3", "h4", "h5", "h6":
			headings[n.Data]++
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		countHeadings(c, headings)
	}
}

// DetectLoginForm analyzes the HTML document to detect login forms
func DetectLoginForm(body io.Reader) (bool, error) {
	doc, err := html.Parse(body)
	if err != nil {
		return false, err
	}

	return hasLoginForm(doc), nil
}

// hasLoginForm recursively searches for login form patterns
func hasLoginForm(n *html.Node) bool {
	if n.Type == html.ElementNode {
		// Check for form elements
		if n.Data == "form" {
			if isLoginForm(n) {
				return true
			}
		}

		// Check for input elements that might indicate login
		if n.Data == "input" {
			if isLoginInput(n) {
				return true
			}
		}
	}

	// Recursively check child nodes
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if hasLoginForm(c) {
			return true
		}
	}

	return false
}

// isLoginForm checks if a form element appears to be a login form
func isLoginForm(formNode *html.Node) bool {
	hasPasswordField := false
	hasUsernameField := false

	// Check form attributes for login indicators
	for _, attr := range formNode.Attr {
		value := strings.ToLower(attr.Val)
		if attr.Key == "action" || attr.Key == "id" || attr.Key == "class" || attr.Key == "name" {
			if strings.Contains(value, "login") || strings.Contains(value, "signin") ||
				strings.Contains(value, "auth") || strings.Contains(value, "session") {
				return true
			}
		}
	}

	// Check for password and username/email fields within the form
	checkFormInputs(formNode, &hasPasswordField, &hasUsernameField)

	// A login form typically has both password and username/email fields
	return hasPasswordField && hasUsernameField
}

// checkFormInputs recursively checks input fields within a form
func checkFormInputs(n *html.Node, hasPassword *bool, hasUsername *bool) {
	if n.Type == html.ElementNode && n.Data == "input" {
		inputType := ""
		inputName := ""
		inputId := ""

		for _, attr := range n.Attr {
			switch attr.Key {
			case "type":
				inputType = strings.ToLower(attr.Val)
			case "name":
				inputName = strings.ToLower(attr.Val)
			case "id":
				inputId = strings.ToLower(attr.Val)
			}
		}

		// Check for password field
		if inputType == "password" {
			*hasPassword = true
		}

		// Check for username/email field
		if inputType == "email" || inputType == "text" {
			if strings.Contains(inputName, "user") || strings.Contains(inputName, "email") ||
				strings.Contains(inputName, "login") || strings.Contains(inputId, "user") ||
				strings.Contains(inputId, "email") || strings.Contains(inputId, "login") {
				*hasUsername = true
			}
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		checkFormInputs(c, hasPassword, hasUsername)
	}
}

// isLoginInput checks if an input element appears to be login-related
func isLoginInput(inputNode *html.Node) bool {
	for _, attr := range inputNode.Attr {
		value := strings.ToLower(attr.Val)

		// Check type attribute
		if attr.Key == "type" && value == "password" {
			return true
		}

		// Check name, id, class attributes for login indicators
		if attr.Key == "name" || attr.Key == "id" || attr.Key == "class" {
			if strings.Contains(value, "password") || strings.Contains(value, "login") ||
				strings.Contains(value, "signin") || strings.Contains(value, "auth") {
				return true
			}
		}
	}

	return false
}
