# Web Page Analyzer

A complete web application built with Go and vanilla JavaScript that analyzes web pages and provides detailed information about their structure and content. The application includes both a REST API backend and a modern web frontend.

## Features

### Analysis Capabilities
- **HTML Version Detection**: Identifies the HTML version used in the document (HTML5, HTML 4.01, XHTML)
- **Page Title Extraction**: Extracts the page title
- **Heading Analysis**: Counts headings (h1-h6) by level
- **Link Analysis**: Categorizes links as internal/external and checks accessibility
- **Login Form Detection**: Detects the presence of login forms

### Frontend Features
- **Modern Web Interface**: Clean, responsive design that works on desktop and mobile
- **Real-time Analysis**: Submit URLs and see results instantly
- **Visual Results Display**: Structured presentation with cards and visual indicators
- **Error Handling**: User-friendly error messages and validation
- **Loading States**: Visual feedback during analysis

## Quick Start

1. **Start the application:**
   ```bash
   go run main.go
   ```

2. **Access the web interface:**
   Open your browser and go to: `http://localhost:8082`

3. **Use the web interface:**
   - Enter any URL (e.g., `https://example.com`)
   - Click "Analyze" to see detailed results
   - View HTML version, title, headings, links, and login form detection

## API Endpoints

The application serves both the frontend and API endpoints:
- **Frontend**: `http://localhost:8082/` (main web interface)
- **API Base**: `http://localhost:8082/api/` (REST endpoints)

### POST /api/analyze

Analyzes a web page and returns detailed information.

**Request:**
```json
{
  "url": "https://example.com"
}
```

**Response:**
```json
{
  "html_version": "HTML5",
  "title": "Example Domain",
  "headings": {
    "h1": 1,
    "h2": 0,
    "h3": 0,
    "h4": 0,
    "h5": 0,
    "h6": 0
  },
  "links": {
    "internal": 0,
    "external": 1,
    "inaccessible": 0
  },
  "has_login_form": false
}
```

**Error Response:**
```json
HTTP 400 Bad Request
{
  "error": "URL is required"
}
```

```json
HTTP 502 Bad Gateway
{
  "error": "Failed to fetch URL: connection refused"
}
```

### GET /api/health

Health check endpoint.

**Response:**
```json
{
  "status": "healthy"
}
```

## Usage Options

### Option 1: Web Interface (Recommended)
1. **Start the server:**
   ```bash
   go run main.go
   ```

2. **Open your browser:**
   Navigate to `http://localhost:8082`

3. **Analyze a webpage:**
   - Enter a URL in the input field
   - Click "Analyze" button
   - View the detailed results

### Option 2: API Direct Access
1. **Start the server:**
   ```bash
   go run main.go
   ```

2. **Test with curl:**
   ```bash
   curl -X POST http://localhost:8082/api/analyze \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com"}'
   ```

3. **Health check:**
   ```bash
   curl http://localhost:8082/api/health
   ```

## Project Structure

```
├── main.go                 # Server entry point and routing
├── handlers/
│   ├── analyze.go         # API request handlers
│   └── analyze_test.go    # Handler tests
├── models/
│   └── types.go           # Data structures
├── utils/
│   ├── analyzer.go        # Main analysis orchestration
│   ├── html_parser.go     # HTML parsing and analysis
│   ├── html_parser_test.go # Parser tests
│   └── link_analyzer.go   # Link extraction and validation
├── static/                # Frontend files
│   ├── index.html         # Main web interface
│   ├── styles.css         # Styling
│   └── script.js          # Frontend JavaScript
└── README.md
```

## Requirements

- Go 1.19 or later
- Internet connection for analyzing external web pages
- Modern web browser (for frontend interface)

## Dependencies

- `golang.org/x/net/html` - HTML parsing

## Error Handling

The API provides meaningful error messages with appropriate HTTP status codes:

- `400 Bad Request` - Invalid request format or missing URL
- `502 Bad Gateway` - Failed to fetch the target URL
- `500 Internal Server Error` - Server-side processing errors

The error messages include the HTTP status code and a useful description as specified in the requirements.

## Development

### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with verbose output
go test ./... -v

# Run specific package tests
go test ./utils -v
```

### Building for Production
```bash
# Build binary
go build -o web-analyzer main.go

# Run the binary
./web-analyzer
```

## Technology Stack

- **Backend**: Go with net/http (standard library)
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **HTML Parsing**: golang.org/x/net/html
- **Architecture**: Single-server application serving both frontend and API
