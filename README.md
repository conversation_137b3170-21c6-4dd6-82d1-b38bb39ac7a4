# Web Page Analyzer

A Golang HTTP REST server that analyzes web pages and provides detailed information about their structure and content.

## Features

- **HTML Version Detection**: Identifies the HTML version used in the document
- **Page Title Extraction**: Extracts the page title
- **Heading Analysis**: Counts headings (h1-h6) by level
- **Link Analysis**: Categorizes links as internal/external and checks accessibility
- **Login Form Detection**: Detects the presence of login forms

## API Endpoints

### POST /analyze

Analyzes a web page and returns detailed information.

**Request:**
```json
{
  "url": "https://example.com"
}
```

**Response:**
```json
{
  "html_version": "HTML5",
  "title": "Example Domain",
  "headings": {
    "h1": 1,
    "h2": 0,
    "h3": 0,
    "h4": 0,
    "h5": 0,
    "h6": 0
  },
  "links": {
    "internal": 0,
    "external": 1,
    "inaccessible": 0
  },
  "has_login_form": false
}
```

**Error Response:**
```json
HTTP 400 Bad Request
{
  "error": "URL is required"
}
```

```json
HTTP 502 Bad Gateway
{
  "error": "Failed to fetch URL: connection refused"
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "healthy"
}
```

## Usage

1. **Start the server:**
   ```bash
   go run main.go
   ```

2. **Test with curl:**
   ```bash
   curl -X POST http://localhost:8080/analyze \
     -H "Content-Type: application/json" \
     -d '{"url": "https://example.com"}'
   ```

## Requirements

- Go 1.19 or later
- Internet connection for analyzing external web pages

## Dependencies

- `golang.org/x/net/html` - HTML parsing

## Error Handling

The API provides meaningful error messages with appropriate HTTP status codes:

- `400 Bad Request` - Invalid request format or missing URL
- `502 Bad Gateway` - Failed to fetch the target URL
- `500 Internal Server Error` - Server-side processing errors

The error messages include the HTTP status code and a useful description as specified in the requirements.
