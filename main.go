package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"web-analyzer/handlers"
)

func main() {
	// Set up routes
	http.HandleFunc("/analyze", handlers.AnalyzeHandler)
	http.HandleFunc("/health", healthHandler)

	// Start server
	port := ":8082"
	fmt.Printf("Server starting on port %s\n", port)
	log.Fatal(http.ListenAndServe(port, nil))
}

// healthHandler provides a simple health check endpoint
func healthHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	response := map[string]string{"status": "healthy"}
	json.NewEncoder(w).Encode(response)
}
