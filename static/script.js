// DOM elements
const analyzeForm = document.getElementById('analyzeForm');
const urlInput = document.getElementById('urlInput');
const analyzeBtn = document.getElementById('analyzeBtn');
const btnText = document.querySelector('.btn-text');
const btnLoading = document.querySelector('.btn-loading');
const resultsSection = document.getElementById('resultsSection');
const resultsContainer = document.getElementById('resultsContainer');
const errorSection = document.getElementById('errorSection');
const errorMessage = document.getElementById('errorMessage');

analyzeForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const url = urlInput.value.trim();

    const validation = validateURLWithMessage(url);
    if (!validation.valid) {
        showError(validation.message);
        return;
    }

    await analyzeURL(url);
});

function isValidURL(string) {
    try {
        const url = new URL(string);
        return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
        return false;
    }
}

function validateURLWithMessage(url) {
    if (!url) {
        return { valid: false, message: 'Please enter a URL to analyze' };
    }

    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return { valid: false, message: 'URL must start with http:// or https://' };
    }

    if (!isValidURL(url)) {
        return { valid: false, message: 'Please enter a valid URL format' };
    }

    return { valid: true, message: '' };
}

// Main analysis function
async function analyzeURL(url) {
    setLoadingState(true);
    hideError();
    hideResults();

    try {
        const response = await fetch('/api/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: url })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`${response.status}: ${errorText}`);
        }

        const data = await response.json();
        displayResults(data);
    } catch (error) {
        // console.error('Analysis error:', error);

        // Provide user-friendly error messages
        let errorMsg = 'Analysis failed';
        if (error.message.includes('Failed to fetch')) {
            errorMsg = 'Unable to connect to the server. Please check your internet connection and try again.';
        } else if (error.message.includes('502')) {
            errorMsg = 'The URL could not be reached. Please check if the website is accessible.';
        } else if (error.message.includes('400')) {
            errorMsg = 'Invalid URL format. Please enter a valid web address.';
        } else if (error.message.includes('timeout')) {
            errorMsg = 'Request timed out. The website may be slow to respond.';
        } else {
            errorMsg = `Analysis failed: ${error.message}`;
        }

        showError(`Analysis failed: ${error.message}`);
    } finally {
        setLoadingState(false);
    }
}

// Set loading state
function setLoadingState(isLoading) {
    analyzeBtn.disabled = isLoading;
    if (isLoading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline';
    } else {
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
    }
}

// Display results
function displayResults(data) {
    resultsContainer.innerHTML = '';

    // HTML Version
    const htmlVersionCard = createResultCard('HTML Version', data.html_version);
    resultsContainer.appendChild(htmlVersionCard);

    // Page Title
    const titleCard = createResultCard('Page Title', data.title || 'No title found');
    resultsContainer.appendChild(titleCard);

    // Headings Analysis
    const headingsCard = createHeadingsCard(data.headings);
    resultsContainer.appendChild(headingsCard);

    // Links Analysis
    const linksCard = createLinksCard(data.links);
    resultsContainer.appendChild(linksCard);

    // Login Form Detection
    const loginCard = createLoginFormCard(data.has_login_form);
    resultsContainer.appendChild(loginCard);

    showResults();
}

// Create a basic result card
function createResultCard(title, value) {
    const card = document.createElement('div');
    card.className = 'result-card';
    card.innerHTML = `
        <h3>${title}</h3>
        <div class="result-value">${value}</div>
    `;
    return card;
}

// Create headings breakdown card
function createHeadingsCard(headings) {
    const card = document.createElement('div');
    card.className = 'result-card';
    
    const totalHeadings = Object.values(headings).reduce((sum, count) => sum + count, 0);
    
    let headingsHTML = '';
    for (let i = 1; i <= 6; i++) {
        const level = `h${i}`;
        const count = headings[level] || 0;
        headingsHTML += `
            <div class="heading-item">
                <div class="heading-level">${level.toUpperCase()}</div>
                <div class="heading-count">${count}</div>
            </div>
        `;
    }

    card.innerHTML = `
        <h3>Headings Analysis</h3>
        <div class="result-value">Total: ${totalHeadings} headings</div>
        <div class="headings-breakdown">
            ${headingsHTML}
        </div>
    `;
    return card;
}

// Create links analysis card
function createLinksCard(links) {
    const card = document.createElement('div');
    card.className = 'result-card';
    
    const totalLinks = links.internal + links.external;
    
    card.innerHTML = `
        <h3>Links Analysis</h3>
        <div class="result-value">Total: ${totalLinks} links</div>
        <div class="links-grid">
            <div class="link-stat">
                <div class="link-stat-label">Internal</div>
                <div class="link-stat-value">${links.internal}</div>
            </div>
            <div class="link-stat">
                <div class="link-stat-label">External</div>
                <div class="link-stat-value">${links.external}</div>
            </div>
            <div class="link-stat">
                <div class="link-stat-label">Inaccessible</div>
                <div class="link-stat-value">${links.inaccessible}</div>
            </div>
        </div>
    `;
    return card;
}

// Create login form detection card
function createLoginFormCard(hasLoginForm) {
    const card = document.createElement('div');
    card.className = 'result-card';
    
    const indicatorClass = hasLoginForm ? 'has-login' : 'no-login';
    const indicatorText = hasLoginForm ? '✓ Login form detected' : '✗ No login form found';
    
    card.innerHTML = `
        <h3>Login Form Detection</h3>
        <div class="login-indicator ${indicatorClass}">
            ${indicatorText}
        </div>
    `;
    return card;
}

// Show results section
function showResults() {
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

// Hide results section
function hideResults() {
    resultsSection.style.display = 'none';
}

// Show error message
function showError(message) {
    errorMessage.textContent = message;
    errorSection.style.display = 'block';
    errorSection.scrollIntoView({ behavior: 'smooth' });
}

// Hide error message
function hideError() {
    errorSection.style.display = 'none';
}

// Auto-focus on URL input when page loads
document.addEventListener('DOMContentLoaded', () => {
    urlInput.focus();
});

// Clear error when user starts typing
urlInput.addEventListener('input', () => {
    if (errorSection.style.display === 'block') {
        hideError();
    }
});
