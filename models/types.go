package models

// AnalyzeRequest represents the request structure for web page analysis
type AnalyzeRequest struct {
	URL string `json:"url"`
}

// AnalyzeResponse represents the response structure for web page analysis
type AnalyzeResponse struct {
	HTMLVersion   string            `json:"html_version"`
	Title         string            `json:"title"`
	Headings      map[string]int    `json:"headings"`
	Links         LinkAnalysis      `json:"links"`
	HasLoginForm  bool              `json:"has_login_form"`
}

// LinkAnalysis contains information about links found on the page
type LinkAnalysis struct {
	Internal      int `json:"internal"`
	External      int `json:"external"`
	Inaccessible  int `json:"inaccessible"`
}

// HeadingCount represents the count of headings by level
type HeadingCount struct {
	H1 int `json:"h1"`
	H2 int `json:"h2"`
	H3 int `json:"h3"`
	H4 int `json:"h4"`
	H5 int `json:"h5"`
	H6 int `json:"h6"`
}
